<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreExpenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:255',
            'amount' => 'required|numeric',
            'currency' => 'required|string|max:3',
            'category' => 'required|string|max:255',
            'date' => 'required|date',
            'frequency' => 'required|string|max:255',
            'project_id' => 'exists:projects,id',
            'status' => 'required|string|max:255',
            'payment_method' => 'required|string|max:255',
        ];
    }
}
