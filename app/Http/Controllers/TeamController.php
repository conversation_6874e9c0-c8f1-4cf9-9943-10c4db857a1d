<?php

namespace App\Http\Controllers;

use App\Actions\Teams\RemoveTeamMember;
use App\Actions\Teams\CreateTeam;
use App\Actions\Teams\DeleteTeam;
use App\Actions\Teams\UpdateTeam;
use App\Http\Requests\Settings\TeamRequest;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;


class TeamController extends Controller
{

    /**
     * Show Create a team page
     */
    public function create()
    {

        return Inertia::render('teams/Create');
    }

    /**
     * Save the team
     */
    public function store(TeamRequest $request, CreateTeam $createTeam): RedirectResponse
    {
        $team = $createTeam->handle($request->validated());

        // Create the team and start trial
        // $team->createAsCustomer([
        //     'trial_ends_at' => now()->addDays(31)
        // ]);

        // TODO: Come back to this
        // $team->createAsCustomer();
        // $team->newSubscription('default', 'pri_01jmbhx1ge98yvtjnjr71f57ey')
        //     ->trialDays(31)
        //     ->create();

        // Redirect to the team edit page after successful creation
        return redirect()->route('team.edit');
    }

    /**
     * Show the user's team settings page.
     */
    public function edit()
    {
        $team = Auth::user()->currentTeam;

        return Inertia::render('settings/Team', [
            'team' => [
                'id' => $team->id,
                'name' => $team->name,
                'email' => $team->email,
                'members' => $team->members()->simplePaginate(10)->through(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->pivot->role
                    ];
                }),
            ],
        ]);
    }

    /**
     * Update the user's team information.
     */
    public function update(TeamRequest $request, Team $team, UpdateTeam $updateTeam): RedirectResponse
    {
        $updateTeam->handle($request->validated(), $team);

        return redirect()->route('team.edit')->with('success', 'Team updated successfully!');
    }

    /**
     * Delete the user's team.
     */
    public function destroy(Team $team, DeleteTeam $deleteTeam)
    {
        // Call the action to delete the team
        $deleteTeam->handle($team);

        // You can redirect or return a response as needed
        return redirect()->route('dashboard')->with('success', 'Team deleted successfully.');
    }

    /**
     * Switch the user's current team.
     */
    public function switchTeam(Request $request)
    {
        $request->validate([
            'team_id' => 'required|exists:teams,id',
        ]);

        $user = $request->user();
        $teamId = $request->team_id;

        // Check if the user is a member of the team
        if (!$user->teams()->where('teams.id', $teamId)->exists()) {
            return redirect()->back()->with('error', 'You are not a member of this team.');
        }

        // Update the current team
        $user->update(['current_team_id' => $teamId]);

        return redirect()->back()->with('success', 'Team switched successfully!');
    }

    /**
     * Remove a team member from a team
     */
    public function removeMember(Team $team, User $member, RemoveTeamMember $remove): RedirectResponse
    {
        $remove->remove(auth()->user(), $team, $member);

        return back()->with('success', 'Team member removed successfully.');
    }

    /**
     * Transfer ownership of a team to another user
     */
    // public function transferOwnership(Request $request, Team $team)
    // {
    //     $request->validate([
    //         'new_owner_id' => 'required|exists:users,id', // Validate that the new owner is a valid user
    //     ]);

    //     // Check if the current user is the owner of the team
    //     $user = $request->user();
    //     if ($user->id !== $team->owner_id) {
    //         return redirect()->back()->with('error', 'You are not the owner of this team.');
    //     }

    //     // Update the owner_id to the new owner
    //     $team->update(['owner_id' => $request->new_owner_id]);

    //     return redirect()->route('teams.show', $team)->with('success', 'Ownership transferred successfully.');
    // }
}
