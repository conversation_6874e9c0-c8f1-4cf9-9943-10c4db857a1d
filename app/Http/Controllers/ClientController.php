<?php

namespace App\Http\Controllers;

use App\Models\Client;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ClientController extends Controller
{
    public function index()
    {
        $companies = Client::where('team_id', auth()->user()->currentTeam->id)->get();

        return Inertia::render('clients/Index', [
            'companies' => $companies,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        $client = Client::where('team_id', auth()->user()->currentTeam->id)->first();

        return Inertia::render('clients/Show', [
            'client' => [
                'id' => $client->id,
                'name' => $client->name,
                'email' => $client->email,
                'phone' => $client->phone,
                'linkedin' => $client->linkedin,
                'website' => $client->website,
                'address' => $client->address,
                'created_by_name' => $client->created_by_name,
                'contacts' => $client->contacts()->get()->map(function ($contact) {
                    return [
                        'id' => $contact->id,
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'phone' => $contact->phone,
                        'job_position' => $contact->job_position,
                    ];
                }),
            ],
        ]);
    }

    /**
     * Display the specified resource.
     */
    // public function show(Company $company)
    // {
    //     return Inertia::render('contacts/Company', [
    //         'company' => [
    //             'id' => $company->id,
    //             'name' => $company->name,
    //             'email' => $company->email,
    //             'phone' => $company->phone,
    //             'slug' => $company->slug,
    //             'contacts' => $company->contacts()->get()->map(function ($contact) {
    //                 return [
    //                     'id' => $contact->id,
    //                     'name' => $contact->name,
    //                     'email' => $contact->email,
    //                     'phone' => $contact->phone,
    //                     'job_position' => $contact->job_position,
    //                 ];
    //             }),
    //         ],
    //     ]);
    // }
}
