<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Team;

class AdminController extends Controller
{
    public function dashboard()
    {
        $userCount = User::count();
        $recentUsers = User::latest()->take(5)->get();
        $teamCount = Team::count();
        $recentTeams = Team::latest()->take(5)->get();

        return view('admin.dashboard', compact('userCount', 'recentUsers', 'teamCount', 'recentTeams',));
    }
}
