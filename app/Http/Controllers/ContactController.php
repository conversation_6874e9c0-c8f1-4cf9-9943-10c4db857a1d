<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreContactRequest;
use App\Http\Requests\UpdateContactRequest;
use App\Models\Client;
use App\Models\Contact;
use Inertia\Inertia;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get all contacts
        $contacts = Contact::where('team_id', auth()->user()->currentTeam->id)->get();
        $companies = Client::where('team_id', auth()->user()->currentTeam->id)->get();


        return Inertia::render('contacts/Index', [
            'contacts' => $contacts,
            'companies' => $companies,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContactRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        $company = $contact->company()->first();

        return Inertia::render('contacts/Show', [
            'contact' => [
                'id' => $contact->id,
                'name' => $contact->name,
                'email' => $contact->email,
                'phone' => $contact->phone,
                'linkedin' => $contact->linkedin,
                'job_position' => $contact->job_position,
                'contact_type' => $contact->contact_type,
                'created_at' => $contact->created_at->format('l jS F Y \a\t H:i A'),
                'updated_at' => $contact->updated_at->format('l jS F Y \a\t H:i A'),
                'created_at_days_ago' => $contact->created_at->diffForHumans(),
                'created_by' => $contact->created_by,
                'created_by_name' => $contact->created_by_name,
            ],
            'company' => $company ? [
                'id' => $company->id,
                'name' => $company->name,
            ] : null,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contact $contact)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContactRequest $request, Contact $contact)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        //
    }
}
