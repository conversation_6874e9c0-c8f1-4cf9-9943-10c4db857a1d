<?php

namespace App\Http\Controllers\Auth;

use App\Actions\Register;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Socialite\Contracts\User as SocialiteUser;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class RegisterUserViaGoogleController extends Controller
{
    // Redirect
    public function create(): RedirectResponse
    {
        return Socialite::driver('google')->redirect();
    }
    
    // Callback
    public function store(Register $register): RedirectResponse
    {
        $googleUser = Socialite::driver('google')->user();

        dd($googleUser);

        // Check if google user has an email
        if (!$googleUser->getEmail()) {
            return redirect(route('register'))->withErrors([
                'email' => 'Oops! It looks like your Google account doesn\'t have an email address associated with it, 
                    or you\'ve selected the "Keep my email addresses private" option. To continue, please:
                    1. Add an email address to your Google account, or
                    2. Uncheck the "Keep my email addresses private" option in your google email settings.
                    If you\'d prefer not to make these changes, you can register using another method, such as your email address.'
                ]);
        }
        
        // If the user is signed in, associate the google
        // token with their account.
        if (($user = Auth::user()) instanceof User) {
            return $this->login($user, $googleUser);
        }
        
        // If, upon registering, the google token already exists in our db,
        // associate those credentials with that account.
        if ($user = User::where(['google_id' => $googleUser->getId()])->first()) {
            return $this->login($user, $googleUser);
        }

        // If we already have an account for that google email address, ask
        // the user to login and try again.
        if (User::where(['email' => $googleUser->getEmail(), 'google_id' => null])->exists()) {
            return redirect(route('login'))->withErrors([
                'email' => 'An account for this email already exists. Please reset your password or login and visit your settings page to add google authentication.',
            ]);
        }

        // Otherwise, register them!
        return $register->handle([
            'name' => $googleUser->getName(),
            'email' => $googleUser->getEmail(),
            'password' => Str::random(16), // Hashing is automatic
            'google_token' => $googleUser->token,
            'google_refresh_token' => $googleUser->refreshToken,
        ]);
    }

    public function login(User $user, SocialiteUser $googleUser): RedirectResponse
    {
        
        $user->update([
            'google_token' => $googleUser->token,
            'google_refresh_token' => $googleUser->refreshToken,
        ]);

        Auth::login($user);

        return redirect(route('dashboard'));
    }
}
