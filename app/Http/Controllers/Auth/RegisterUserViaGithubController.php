<?php

namespace App\Http\Controllers\Auth;

use App\Actions\Register;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Socialite\Contracts\User as SocialiteUser;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class RegisterUserViaGithubController extends Controller
{
    // Redirect
    public function create(): RedirectResponse
    {
        return Socialite::driver('github')->redirect();
    }
    
    // Callback
    public function store(Register $register): RedirectResponse
    {
        $githubUser = Socialite::driver('github')->user();

        // Check if GitHub user has an email
        if (!$githubUser->getEmail()) {
            return redirect(route('register'))->withErrors([
                'email' => 'Oops! It looks like your GitHub account doesn\'t have an email address associated with it, 
                    or you\'ve selected the "Keep my email addresses private" option. To continue, please:
                    1. Add an email address to your GitHub account, or
                    2. Uncheck the "Keep my email addresses private" option in your GitHub email settings.
                    If you\'d prefer not to make these changes, you can register using another method, such as your email address.'
                ]);
        }
        
        // If the user is signed in, associate the GitHub
        // token with their account.
        if (($user = Auth::user()) instanceof User) {
            return $this->login($user, $githubUser);
        }
        
        // If, upon registering, the GitHub token already exists in our db,
        // associate those credentials with that account.
        if ($user = User::where(['github_id' => $githubUser->getId()])->first()) {
            return $this->login($user, $githubUser);
        }

        // If we already have an account for that GitHub email address, ask
        // the user to login and try again.
        if (User::where(['email' => $githubUser->getEmail(), 'github_id' => null])->exists()) {
            return redirect(route('login'))->withErrors([
                'email' => 'An account for this email already exists. Please reset your password or login and visit your settings page to add Github authentication.',
            ]);
        }

        // Otherwise, register them!
        return $register->handle([
            'name' => $githubUser->getName(),
            'email' => $githubUser->getEmail(),
            'password' => Str::random(16), // Hashing is automatic
            'github_id' => $githubUser->getId(),
            'github_token' => $githubUser->token,
        ]);
    }

    public function login(User $user, SocialiteUser $githubUser): RedirectResponse
    {
        
        $user->update([
            'github_id' => $githubUser->getId(),
            'github_token' => $githubUser->token,
        ]);

        Auth::login($user);

        return redirect(route('dashboard'));
    }
}
