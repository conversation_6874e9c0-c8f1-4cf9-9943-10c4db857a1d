<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class OnboardingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show()
    {
        return Inertia::render('onboarding/Index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function handleStep1(Request $request)
    {
        $request->validate([
            'action' => 'required|in:create,join',
        ]);

        session(['onboarding_action' => $request->action]);

        return redirect()->route('onboarding.Billing');
    }

    public function billing()
    {
        return inertia('onboarding/Billing');
    }

    public function handleBilling(Request $request)
    {
        $user = auth()->user();
        if (!$user->subscribed('default')) {
            return redirect()->route('onboarding.Billing');
        }

        return redirect()->route('onboarding.team');
    }

    public function createTeam()
    {
        return inertia('onboarding/Create');
    }

    public function handleTeam(Request $request)
    {
        $request->validate([
            'emails' => 'nullable|array',
            'emails.*' => 'email',
        ]);

        // Invite team members logic here

        return redirect()->route('dashboard')->with('success', 'Onboarding complete!');
    }
}
