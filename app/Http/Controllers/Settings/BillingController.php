<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BillingController extends Controller
{
    public function index(Request $request)
    {
        $team = $request->user()->currentTeam;

        $transactions = $team->transactions()
            ->orderByDesc('billed_at')
            ->get()
            ->map(fn($transaction) => [
                'id' => $transaction->id,
                'billable_id' => $transaction->billable_id,
                'billable_type' => $transaction->billable_type,
                'paddle_id' => $transaction->paddle_id,
                'paddle_subscription_id' => $transaction->paddle_subscription_id,
                'invoice_number' => $transaction->invoice_number,
                'status' => $transaction->status,
                'total' => $transaction->total,
                'tax' => $transaction->tax,
                'currency' => $transaction->currency,
                'billed_at' => $transaction->billed_at,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at,
            ]);

        return inertia('settings/billing/Index', [
            'user' => $request->user(),
            'subscription' => $team->subscription('default'),
            'transactions' => $transactions,
        ]);
    }

    public function cancel(Request $request)
    {
        $team = $request->user()->currentTeam;
        $subscription = $team->subscription('default');

        if (!$subscription) {
            return back()->with('error', 'No active subscription found.');
        }

        // Cancel at period end
        $subscription->cancel();

        return back()->with('success', 'Your subscription has been cancelled and will end at the current billing period.');
    }
}
