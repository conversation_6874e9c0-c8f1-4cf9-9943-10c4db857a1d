<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Show the user's profile settings page.
     */
    public function edit(Request $request): Response
    {
        return Inertia::render('settings/Profile', [
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return to_route('profile.edit');
    }

    /**
     * Delete the user's profile.
     */
    public function destroy(Request $request): RedirectResponse
    {
        // TODO: Need to sort this out - user can be owner of multiple teams and some teams might need ownership transferred etc
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        $deleteTeam = false;

        // Check if the user is the owner of any team
        $teams = $user->ownedTeams();
        if ($teams->count() > 0) {
            foreach ($teams as $team) {
                if ($team->members()->count() > 1) {
                    // If the team has more than one member, prompt for the transfer
                    // This could be a UI prompt where the owner selects a new team member
                    $newOwner = $team->members()->where('user_id', '!=', $user->id)->first(); // Automatically assign the first available member

                    // Transfer ownership
                    $team->update(['owner_id' => $newOwner->id]);
                } else {
                    // If the team has only one member, set delete team to true
                    $deleteTeam = true;
                }
            }
        }

        Auth::logout();

        // If delete team is true, delete the team
        $deleteTeam ?? $team->delete();
        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function unlinkGithub(Request $request): RedirectResponse
    {
        $user = $request->user();
        
        $user->update([
            'github_id' => null,
            'github_token' => null,
        ]);

        return to_route('profile.edit');
    }
}
