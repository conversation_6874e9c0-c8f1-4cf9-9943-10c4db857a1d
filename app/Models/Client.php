<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    /** @use HasFactory<\Database\Factories\ClientFactory> */
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = ['name', 'email', 'phone'];

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by_name');
    }
}
