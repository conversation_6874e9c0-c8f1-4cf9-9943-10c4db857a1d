<?php

namespace App\Services;

use App\Models\Team;
use Illuminate\Support\Str;

class TeamSlugService
{
    /**
     * Generate a unique slug for the team
     */
    public static function generate(string $name, ?Team $ignoreTeam = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug . '-' . rand(1000, 9999);

        // Keep generating new slugs until we find a unique one
        while (self::slugExists($slug, $ignoreTeam)) {
            $slug = $baseSlug . '-' . rand(1000, 9999);
        }

        return $slug;
    }

    /**
     * Check if a slug exists, optionally ignoring a specific team
     */
    private static function slugExists(string $slug, ?Team $ignoreTeam = null): bool
    {
        $query = Team::where('slug', $slug);
        
        if ($ignoreTeam) {
            $query->where('id', '!=', $ignoreTeam->id);
        }

        return $query->exists();
    }
}