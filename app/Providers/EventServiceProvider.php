<?php

namespace App\Providers;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Laravel\Paddle\Events\WebhookReceived;
use Laravel\Paddle\Events\WebhookHandled;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Event::listen(function (WebhookReceived $event) {
            Log::info('Paddle webhook received', [
                'type' => $event->payload['type'] ?? 'unknown',
                'data' => $event->payload,
            ]);
        });

        Event::listen(function (WebhookHandled $event) {
            Log::info('Paddle webhook handled', [
                'type' => $event->payload['type'] ?? 'unknown',
                'data' => $event->payload,
            ]);
        });
    }
}
