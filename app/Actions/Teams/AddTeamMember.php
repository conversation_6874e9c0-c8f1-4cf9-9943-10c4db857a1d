<?php

namespace App\Actions\Teams;

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;

class AddTeamMember
{
    /**
     * Add a new member to the team.
     *
     * @param  \App\Models\User  $adminUser  The user adding the member.
     * @param  \App\Models\Team  $team  The team to which the member is added.
     * @param  string  $email  The email of the user being added.
     * @param  string  $role  The role of the new team member.
     * @return void
     */
    public function add(User $adminUser, Team $team, string $email, string $role = 'member')
    {
        // Ensure the current user is authorized to add members
        if (!Gate::forUser($adminUser)->allows('add-team-member', $team)) {
            throw ValidationException::withMessages(['error' => 'You do not have permission to add members to this team.']);
        }

        // Find the user by email
        $user = User::where('email', $email)->first();

        if (!$user) {
            throw ValidationException::withMessages(['email' => 'User not found.']);
        }

        // Check if the user is already in the team
        if ($team->members()->where('user_id', $user->id)->exists()) {
            throw ValidationException::withMessages(['email' => 'This user is already a team member.']);
        }

        // Attach user to the team with the given role
        $team->members()->attach($user->id, ['role' => $role]);
    }
}