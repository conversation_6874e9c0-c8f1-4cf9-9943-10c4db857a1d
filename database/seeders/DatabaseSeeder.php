<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\Contact;
use App\Models\Expense;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Models\Team;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create 10 random users
        User::factory(10)->create();

        // Create 10 clients
        Client::factory(10)->create();

        // Create 10 contacts
        Contact::factory(10)->create();

        // Create 10 projects
        $projects = Project::factory(10)->create();

        // Create 10 tasks for each of the projects
        $projects->each(function ($project) {
            $project->tasks()->createMany(
                Task::factory(10)->make()->toArray()
            );
        });

        // Create a test user
        $testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        // Create a team for the test user
        $team = Team::factory()->create([
            'email' => $testUser->email,
            'owner_id' => $testUser->id,
        ]);


        // Add the test user as a member of the team
        $team->members()->attach($testUser->id, ['role' => 'admin']);

        // Assign the created team as the test user's current team
        $testUser->update(['current_team_id' => $team->id]);

        // Create 5 projects for the test user
        Project::factory(5)->create(['team_id' => $team->id]);

        // Get all projects for the test user's team and create tasks for each
        Project::where('team_id', $team->id)->get()->each(function ($project) {
            $project->tasks()->createMany(
                Task::factory(10)->make()->toArray()
            );
        });

        Expense::factory(10)->create(['team_id' => $team->id]);

        // Create 10 clients for the test user
        Client::factory(10)->create(['team_id' => $team->id]);

        // Create 10 contacts for the test user
        Contact::factory(10)->create(['team_id' => $team->id]);
    }
}
