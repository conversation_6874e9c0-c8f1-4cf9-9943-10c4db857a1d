<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => (string) Str::uuid(),
            'project_id' => \App\Models\Project::factory(),
            'name' => fake()->company(),
            'description' => fake()->sentence(),
            'created_by_name' => fake()->name(),
            'status' => fake()->randomElement(['backlog', 'todo', 'in_progress', 'review', 'done']),
            'due_date' => fake()->date(),
        ];
    }
}
