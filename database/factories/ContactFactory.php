<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Contact>
 */
class ContactFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => (string) Str::uuid(),
            'team_id' => Team::factory(),
            'client_id' => self::getClientId(),
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'job_position' => fake()->jobTitle(),
            'linkedin' => fake()->url(),
            'contact_type' => fake()->randomElement(['customer', 'lead', 'partner', null]),
            'created_by_name' => fake()->name(),
        ];
    }

    /**
     * Randomly assign an client ID or null.
     */
    protected static function getClientId(): ?string
    {
        // 50% chance to assign an organisation, 50% chance to leave it null
        return fake()->boolean(50) ? Client::inRandomOrder()->first()->id : null;
    }
}
