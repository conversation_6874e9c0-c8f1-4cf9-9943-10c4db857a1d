<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Expense>
 */
class ExpenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => (string) Str::uuid(),
            'team_id' => \App\Models\Team::factory(),
            'name' => fake()->company(),
            'description' => fake()->sentence(),
            'amount' => fake()->randomFloat(2, 0, 1000),
            'currency' => 'USD',
            'category' => fake()->word(),
            'date' => fake()->date(),
            'frequency' => fake()->randomElement(['once', 'daily', 'weekly', 'monthly', 'yearly']),
            'project_id' => \App\Models\Project::factory(),
            'status' => fake()->randomElement(['pending', 'approved', 'rejected', 'paid', 'cancelled', 'processing', 'failed', 'expired', 'completed']),
            'payment_method' => fake()->randomElement(['cash', 'card', 'bank', 'paypal', 'stripe']),
            'created_by_name' => fake()->name(),
        ];
    }
}
