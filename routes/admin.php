<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminTeamController;
use App\Http\Controllers\Admin\AdminUserController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::redirect('/', 'admin/dashboard');

    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Users
    Route::controller(AdminUserController::class)->group(function () {
        Route::get('/users', 'index')->name('users.index');
        // Route::get('/users/create', 'create')->name('users.create');
        // Route::post('/users', 'store')->name('users.store');
        Route::get('/users/{user}', 'show')->name('users.show');
        Route::get('/users/{user}/edit', 'edit')->name('users.edit');
        Route::patch('/users/{user}', 'update')->name('users.update');
        Route::delete('/users/{user}', 'destroy')->name('users.destroy');
    });

    // Teams
    Route::controller(AdminTeamController::class)->group(function () {
        Route::get('/teams', 'index')->name('teams.index');
        // Route::get('/teams/create', 'create')->name('teams.create');
        // Route::post('/teams', 'store')->name('teams.store');
        Route::get('/teams/{team}', 'show')->name('teams.show');
        Route::get('/teams/{team}/edit', 'edit')->name('teams.edit');
        Route::patch('/teams/{team}', 'update')->name('teams.update');
        Route::delete('/teams/{team}', 'destroy')->name('teams.destroy');
    });
});
