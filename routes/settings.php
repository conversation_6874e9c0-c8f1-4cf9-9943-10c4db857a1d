<?php

use App\Http\Controllers\Settings\BillingController;
use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\Settings\ProfileController;
use App\Http\Controllers\TeamController;
use Illuminate\Support\Facades\Route;
use <PERSON><PERSON>\Paddle\Transaction;
use Inertia\Inertia;

Route::middleware('auth', 'verified', 'ensure.team')->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');

    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');

    Route::patch('settings/profile', [ProfileController::class, 'unlinkGithub'])->name('profile.unlinkGithub');

    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('settings/password', [PasswordController::class, 'edit'])->name('password.edit');

    Route::put('settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/Appearance');
    })->name('appearance');

    Route::get('settings/intigrations', function () {
        return Inertia::render('settings/Intigrations');
    })->name('intigrations');

    Route::get('settings/team', [TeamController::class, 'edit'])->name('team.edit');

    Route::patch('settings/team/{team}', [TeamController::class, 'update'])->name('team.update');

    Route::put('settings/teams/switch', [TeamController::class, 'switchTeam'])->name('teams.switchTeam');

    Route::delete('settings/teams/{team}/members/{member}', [TeamController::class, 'removeMember'])->name('team.members.remove');

    // Route::get('settings/billing', function () {
    //     return Inertia::render('settings/billing/Index');
    // })->name('team.billing');

    Route::get('settings/billing', [BillingController::class, 'index'])->name('team.billing');
    Route::get('settings/billing/cancel', [BillingController::class, 'cancel'])->name('team.cancel');


    Route::get('/settings/billing/invoice/{transaction}', function (Transaction $transaction) {
        return $transaction->redirectToInvoicePdf();
    })->name('download-invoice');
});
