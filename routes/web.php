<?php

use App\Http\Controllers\Auth\RegisterUserViaGithubController;
use App\Http\Controllers\Auth\RegisterUserViaGoogleController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\OnboardingController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\TeamController;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use Laravel\Paddle\Http\Controllers\WebhookController;

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
require __DIR__ . '/admin.php';

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

// Get the terms of service page
Route::get('terms', function () {
    return Inertia::render('TermsOfService');
})->name('terms');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified', 'ensure.team'])->name('dashboard');

Route::get('docs', function () {
    return Inertia::render('Docs/Index');
})->name('docs');



// SSO SOCIALITE
Route::get('auth/github/redirect', [RegisterUserViaGithubController::class, 'create']);

Route::get('auth/github/callback', [RegisterUserViaGithubController::class, 'store']);

Route::get('auth/google/redirect', [RegisterUserViaGoogleController::class, 'create']);

Route::get('auth/google/callback', [RegisterUserViaGoogleController::class, 'store']);

// TEAMS
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('teams', TeamController::class);
});

Route::middleware(['auth'])->group(function () {
    Route::get('/onboarding', [OnboardingController::class, 'show'])->name('onboarding');

    Route::post('/onboarding', [OnboardingController::class, 'handleStep1']);

    Route::get('/onboarding/billing', [OnboardingController::class, 'billing'])->name('onboarding.Billing');

    Route::post('/onboarding/billing', [OnboardingController::class, 'handleBilling']);

    Route::get('/onboarding/create-team', [OnboardingController::class, 'createTeam'])->name('onboarding.Create');

    Route::post('/onboarding/create-team', [OnboardingController::class, 'handleTeam']);
});

// Subscriptions
// https://stackoverflow.com/questions/79480056/laravel-cashier-paddle-not-storing-transactions-subscriptions
// Route::post('/paddle/webhook', WebhookController::class); // "this overrides the default behavior of Cashier"
Route::post('/paddle/webhook', WebhookController::class)->name('cashier.webhook');;

// Paddle
Route::get('/subscribe', function (Request $request) {
    $team = Team::find($request->user()->current_team_id);

    $team->createAsCustomer();

    $checkout = $team->subscribe($premium = 'pri_01jmbhx1ge98yvtjnjr71f57ey', 'default')
        ->returnTo(route('dashboard'));

    return Inertia::render('Subscribe', [
        'checkout' => [
            'options' => [
                'items' => $checkout->getItems(),
                'customerId' => optional($checkout->getCustomer())->paddle_id,
                'customerEmail' => optional($checkout->getCustomer())->email,
                'customerName' => optional($checkout->getCustomer())->name,
                'customData' => $checkout->getCustomData(),
                'successUrl' => $checkout->getReturnUrl(),
            ],
        ],
    ]);
})->name('subscribe');


// Contacts and companies
Route::middleware(['auth', 'ensure.team'])->group(function () {
    // Route::resource('contacts', ContactController::class);

    // Redirect if user goes to contacts/c or contacts/o instead of contacts/c/id and redirect to /contacts
    Route::get('contacts/c', [ContactController::class, 'index'])->name('contacts.index');
    Route::get('contacts/o', [ContactController::class, 'index'])->name('contacts.index');

    Route::get('contacts', [ContactController::class, 'index'])->name('contacts.index');
    Route::get('contacts/c/{contact}', [ContactController::class, 'show'])->name('contacts.show');

    // Route::get('contacts/o/{company}', [CompanyController::class, 'show'])->name('company.show');

    // Clients
    Route::get('/clients', [ClientController::class, 'index'])->name('clients.Index');
    Route::get('/clients/{client}', [ClientController::class, 'show'])->name('client.Show');
});


// Projects and Taks
Route::middleware(['auth'])->group(function () {
    // Projects
    Route::get('/projects', [ProjectController::class, 'index'])->name('projects.Index'); // List projects
    Route::get('/projects/create', [ProjectController::class, 'create'])->name('projects.create');
    Route::post('/projects', [ProjectController::class, 'store'])->name('projects.store');
    Route::get('/projects/{project}', [ProjectController::class, 'show'])->name('projects.show'); // Show project & tasks
    Route::get('/projects/{project}/board', [ProjectController::class, 'board'])->name('projects.board'); // Show project board

    Route::get('/projects/{project}/edit', [ProjectController::class, 'edit'])->name('projects.edit');
    Route::put('/projects/{project}', [ProjectController::class, 'update'])->name('projects.update');
    Route::delete('/projects/{project}', [ProjectController::class, 'destroy'])->name('projects.destroy');

    // Tasks (Scoped under Projects)
    Route::post('/projects/{project}/tasks', [TaskController::class, 'store'])->name('tasks.store');
    Route::put('/projects/{project}/tasks/{task}', [TaskController::class, 'update'])->name('tasks.update');
    Route::delete('/projects/{project}/tasks/{task}', [TaskController::class, 'destroy'])->name('tasks.destroy');

    // Expenses
    Route::get('/expenses', [ExpenseController::class, 'index'])->name('expenses.Index');
});
