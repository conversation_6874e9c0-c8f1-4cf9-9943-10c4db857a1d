@tailwind base;
@tailwind components;
@tailwind utilities;

body,
html {
    --font-sans: 'Ubuntu', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 224 71.4% 4.1%;
        --card: 0 0% 100%;
        --card-foreground: 224 71.4% 4.1%;
        --popover: 0 0% 100%;
        --popover-foreground: 224 71.4% 4.1%;
        --primary: 262.1 83.3% 57.8%;
        --primary-foreground: 210 20% 98%;
        --secondary: 220 14.3% 95.9%;
        --secondary-foreground: 220.9 39.3% 11%;
        --muted: 220 14.3% 95.9%;
        --muted-foreground: 220 8.9% 46.1%;
        --accent: 220 14.3% 95.9%;
        --accent-foreground: 220.9 39.3% 11%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 20% 98%;
        --border: 220 13% 91%;
        --input: 220 13% 91%;
        --ring: 262.1 83.3% 57.8%;
        --radius: 0.5rem;
        --sidebar-background: 220 14.3% 95.9%;
        --sidebar-foreground: 220.9 39.3% 11%;
        --sidebar-primary: 262.1 83.3% 57.8%;
        --sidebar-primary-foreground: 210 20% 98%;
        --sidebar-accent: 220 14.3% 95.9%;
        --sidebar-accent-foreground: 220.9 39.3% 11%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 263.4 70% 50.4%;
    }

    .dark {
        --background: 224 71.4% 4.1%;
        --foreground: 210 20% 98%;
        --card: 224 71.4% 4.1%;
        --card-foreground: 210 20% 98%;
        --popover: 224 71.4% 4.1%;
        --popover-foreground: 210 20% 98%;
        --primary: 263.4 70% 50.4%;
        --primary-foreground: 210 20% 98%;
        --secondary: 215 27.9% 16.9%;
        --secondary-foreground: 210 20% 98%;
        --muted: 215 27.9% 16.9%;
        --muted-foreground: 217.9 10.6% 64.9%;
        --accent: 215 27.9% 16.9%;
        --accent-foreground: 210 20% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 20% 98%;
        --border: 215 27.9% 16.9%;
        --input: 215 27.9% 16.9%;
        --ring: 263.4 70% 50.4%;
        --sidebar-background: 215 27.9% 16.9%;
        --sidebar-foreground: 210 20% 98%;
        --sidebar-primary: 263.4 70% 50.4%;
        --sidebar-primary-foreground: 210 20% 98%;
        --sidebar-accent: 215 27.9% 16.9%;
        --sidebar-accent-foreground: 210 20% 98%;
        --sidebar-border: 215 27.9% 16.9%;
        --sidebar-ring: 263.4 70% 50.4%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    .btn-primary {
        @apply inline-flex h-9 items-center justify-center gap-2 whitespace-nowrap rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0;
    }
    .btn-danger {
        @apply inline-flex h-9 items-center justify-center gap-2 whitespace-nowrap rounded-md bg-destructive px-4 py-2 text-sm font-medium text-destructive-foreground shadow-sm ring-offset-background transition-colors hover:bg-destructive/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0;
    }

    .link {
        @apply text-primary underline hover:no-underline;
    }
}

/* Ubuntu Regular */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-Regular.ttf') format('ttf');
    font-weight: 400;
    font-style: normal;
}

/* Ubuntu Italic */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-Italic.ttf') format('ttf');
    font-weight: 400;
    font-style: italic;
}

/* Ubuntu Light */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-Light.ttf') format('ttf');
    font-weight: 300;
    font-style: normal;
}

/* Ubuntu Light Italic */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-LightItalic.ttf') format('ttf');
    font-weight: 300;
    font-style: italic;
}

/* Ubuntu Medium */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-Medium.ttf') format('ttf');
    font-weight: 500;
    font-style: normal;
}

/* Ubuntu Medium Italic */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-MediumItalic.ttf') format('ttf');
    font-weight: 500;
    font-style: italic;
}

/* Ubuntu Bold */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-Bold.ttf') format('ttf');
    font-weight: 700;
    font-style: normal;
}

/* Ubuntu Bold Italic */
@font-face {
    font-family: 'Ubuntu';
    src: url('/fonts/Ubuntu-BoldItalic.ttf') format('ttf');
    font-weight: 700;
    font-style: italic;
}
