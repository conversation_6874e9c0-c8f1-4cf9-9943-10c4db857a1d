@extends('admin.layouts.admin')

@section('title', 'Dashboard')

@section('content')

        <div class="col-span-1 bg-white p-4 rounded shadow">
            <h3 class="font-bold">Total Users</h3>
            <p class="text-2xl">{{ $userCount }}</p>
        </div>
        <div class="col-span-1 bg-white p-4 rounded shadow">
            <h3 class="font-bold">Total Teams</h3>
            <p class="text-2xl">{{ $teamCount }} / 60</p>
        </div>
        <div class="col-span-1 bg-white p-4 rounded shadow">
            <h3 class="font-bold"></h3>
            <p class="text-2xl"></p>
        </div>
    
    <div class="col-span-3 bg-white p-4 rounded shadow mb-6">
        <h3 class="font-bold mb-2">Recent Users</h3>
        <table class="w-full">
            <thead>
                <tr>
                    <th class="text-left">Name</th>
                    <th class="text-left">Email</th>
                    <th class="text-left">Joined</th>
                </tr>
            </thead>
            <tbody>
                @foreach($recentUsers as $user)
                <tr>
                    <td>{{ $user->name }}</td>
                    <td>{{ $user->email }}</td>
                    <td>{{ $user->created_at->diffForHumans() }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="col-span-3 bg-white p-4 rounded shadow mb-6">
        <h3 class="font-bold mb-2">Recent Teams</h3>
        <table class="w-full">
            <thead>
                <tr>
                    <th class="text-left">Name</th>
                    <th class="text-left">Joined</th>
                </tr>
            </thead>
            <tbody>
                @foreach($recentTeams as $team)
                <tr>
                    <td>{{ $team->name }}</td>
                    <td>{{ $team->created_at->diffForHumans() }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endsection