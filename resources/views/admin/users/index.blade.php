@extends('admin.layouts.admin')

@section('title', 'Users')

@section('content')

        <div class="col-span-3">
            <form method="GET" action="{{ route('admin.users.index') }}" class="mb-3">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="Search users..." class="form-control">
                <button type="submit" class="btn btn-primary mt-2">Search</button>
            </form>
        </div>

        <table class="table-auto col-span-3">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>ID</th>
                    <th>Created At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $user)
                    <tr>
                        <td> <a href="{{ route('admin.users.show', $user) }}">{{ $user->name }}</a></td>
                        <td>{{ $user->id }}</td>
                        <td>{{ $user->created_at->format('d M Y') }}</td>
                        <td>
                            <div class="flex space-x-2 justify-end items-center">
                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-warning btn-sm">View</a>
                                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning btn-sm">Edit</a>
                                <form action="{{ route('admin.users.destroy', $user) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Pagination Links -->
        <div class="col-span-3 flex gap-2 justify-end">
            {{ $users->links() }}
        </div>
@endsection