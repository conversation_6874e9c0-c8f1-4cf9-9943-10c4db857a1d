<script setup lang="ts">
import { ScrollArea } from '@/components/ui/scroll-area';
import { Head, <PERSON> } from '@inertiajs/vue3';
</script>

<template>
    <Head title="Docs">
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    </Head>
    <div class="flex min-h-screen flex-col items-center bg-[#FDFDFC] p-6 text-[#1b1b18] dark:bg-[#0a0a0a] lg:justify-center lg:p-8">
        <header class="not-has-[nav]:hidden mb-6 w-full max-w-[335px] text-sm lg:max-w-4xl">
            <nav class="flex items-center justify-end gap-4">
                <Link
                    v-if="$page.props.auth.user.id"
                    :href="route('dashboard')"
                    class="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                >
                    Dashboard
                </Link>
                <template v-else>
                    <Link
                        :href="route('login')"
                        class="inline-block rounded-sm border border-transparent px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#19140035] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                    >
                        Log in
                    </Link>
                    <Link
                        :href="route('register')"
                        class="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                    >
                        Register
                    </Link>
                </template>
            </nav>
        </header>
        <div class="duration-750 starting:opacity-0 flex w-full items-center justify-center opacity-100 transition-opacity lg:grow">
            <aside>
                <ScrollArea class="h-[200px] w-[350px] rounded-md border p-4">
                    Jokester began sneaking into the castle in the middle of the night and leaving jokes all over the place: under the king's pillow,
                    in his soup, even in the royal toilet. The king was furious, but he couldn't seem to stop Jokester. And then, one day, the people
                    of the kingdom discovered that the jokes left by Jokester were so funny that they couldn't help but laugh. And once they started
                    laughing, they couldn't stop.
                </ScrollArea>
            </aside>
            <main class="flex w-full max-w-[335px] flex-col-reverse overflow-hidden lg:max-w-4xl lg:flex-row">
                <input type="text" id="search" placeholder="Search docs" />
                Docs
            </main>
        </div>
        <div class="h-14.5 hidden lg:block"></div>
    </div>
</template>
