<script setup>
import FeatureList from '@/components/FeatureList.vue';
import { router } from '@inertiajs/vue3';

const subscribe = () => {
    router.post(route('onboarding.handleBilling'));
};
</script>

<template>
    <div class="grid h-screen place-items-center">
        <div class="flex max-w-lg flex-col items-center gap-6">
            <h1>Subscription Required</h1>
            <p>Please subscribe to continue.</p>
            <div class="flex flex-col gap-4 border p-6">
                <h2>Subscription Details</h2>
                <FeatureList />

                <h2 class="text-xl">$25<small>per month</small></h2>

                <!-- <Button @click="subscribe">Proceed with Subscription</Button> -->

                <!-- <PaddleButton :checkout="checkoutData" class="bg-blue-500 px-8 py-4 text-white hover:bg-blue-600"> Start Premium Plan </PaddleButton> -->
            </div>
        </div>
    </div>
</template>

<!-- linear-gradient(135deg, #fff 0%, #f3f6f8 100%) -->

<!-- content: '';
position: fixed;
left: -22%;
top: 60%;
right: 0;
width: 125%;
height: 100%;
background: linear-gradient(135deg, #f3f6f8 0%, #fff 100%);
transform: rotate(15deg); -->
