<script setup>
import { But<PERSON> } from '@/components/ui/button';
import { Stepper, StepperDescription, StepperItem, StepperSeparator, StepperTitle, StepperTrigger } from '@/components/ui/stepper';
import { router } from '@inertiajs/vue3';
import { Check, Circle, Dot } from 'lucide-vue-next';
import { ref } from 'vue';

const steps = [
    {
        step: 1,
        title: 'Join or create',
        description: 'Join an existing team or create a new one',
    },
    {
        step: 2,
        title: 'Billing details',
        description: 'Add your billing details',
    },
    {
        step: 3,
        title: 'Invite your team',
        description: 'Start collaborating with your team',
    },
];

const action = ref('');

const submit = () => {
    router.post(route('onboarding'), { action: action.value });
};
</script>

<template>
    <div class="grid h-screen place-items-center">
        <div class="flex max-w-lg flex-col items-center gap-6">
            <Stepper class="flex w-full items-start gap-2">
                <StepperItem
                    v-for="step in steps"
                    :key="step.step"
                    v-slot="{ state }"
                    class="relative flex w-full flex-col items-center justify-center"
                    :step="step.step"
                >
                    <StepperSeparator
                        v-if="step.step !== steps[steps.length - 1].step"
                        class="absolute left-[calc(50%+20px)] right-[calc(-50%+10px)] top-5 block h-0.5 shrink-0 rounded-full bg-muted group-data-[state=completed]:bg-primary"
                    />

                    <StepperTrigger as-child>
                        <Button
                            :variant="state === 'completed' || state === 'active' ? 'default' : 'outline'"
                            size="icon"
                            class="z-10 shrink-0 rounded-full"
                            :class="[state === 'active' && 'ring-2 ring-ring ring-offset-2 ring-offset-background']"
                        >
                            <Check v-if="state === 'completed'" class="size-5" />
                            <Circle v-if="state === 'active'" />
                            <Dot v-if="state === 'inactive'" />
                        </Button>
                    </StepperTrigger>

                    <div class="mt-5 flex flex-col items-center text-center">
                        <StepperTitle :class="[state === 'active' && 'text-primary']" class="text-sm font-semibold transition lg:text-base">
                            {{ step.title }}
                        </StepperTitle>
                        <StepperDescription
                            :class="[state === 'active' && 'text-primary']"
                            class="sr-only text-xs text-muted-foreground transition md:not-sr-only lg:text-sm"
                        >
                            {{ step.description }}
                        </StepperDescription>
                    </div>
                </StepperItem>
            </Stepper>

            <h1 class="text-2xl">Do you want to create a team or join one?</h1>
            <div class="flex gap-4">
                <Button @click="action = 'create'">Create a Team</Button>
                <Button @click="action = 'join'">Join a Team</Button>
            </div>
            <Button @click="submit">Next</Button>
        </div>
    </div>
</template>
