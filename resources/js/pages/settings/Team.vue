<script setup lang="ts">
import { Head } from '@inertiajs/vue3';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';

import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import TeamMembers from './Components/TeamMembers.vue';
import UpdateTeamNameForm from './Components/UpdateTeamNameForm.vue';
import ViewAllTeams from './Components/ViewAllTeams.vue';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Settings',
        href: '/settings',
    },
    {
        title: 'Team settings',
        href: '/settings/team',
    },
];

const props = defineProps({
    team: {
        type: Object,
        required: true,
    },
});
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Team settings" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall title="Team settings" description="Update your team settings" />

                <div class="md:max-w-2xl">
                    <UpdateTeamNameForm :team="team" />
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <TeamMembers :members="team.members" />
                    <div>
                        <ViewAllTeams />
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
