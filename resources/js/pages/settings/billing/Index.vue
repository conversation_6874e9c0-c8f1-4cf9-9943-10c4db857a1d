<script setup lang="ts">
import HeadingSmall from '@/components/HeadingSmall.vue';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Subscription, Transaction, User, type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import CancelSubscription from './Components/CancelSubscription.vue';

interface Props {
    user: User;
    subscription: Subscription | null;
    transactions: Transaction[];
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Settings',
        href: '/settings',
    },
    {
        title: 'Billing',
        href: '/settings/billing',
    },
];

// Helper function to format currency
const formatCurrency = (amount: string, currency: string) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
    }).format(parseFloat(amount));
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Billing settings" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall title="Billing information" description="Update and view your billing information" />

                <ul class="list-inside list-disc space-y-2">
                    <li>[X] Able to Subscribe</li>
                    <li>[X] See current subscription</li>
                    <li>[ ] Cancel subscription</li>
                    <li>[ ] Can resume subscription</li>
                    <li>[ ] Update payment information</li>
                    <li>[X] See invoices history</li>
                    <li>[ ] Download PDF invoice</li>
                    <li>[ ] Show previous and next payment</li>
                    <li>TODO: Upgrade/dopwngrade subscription (storage)</li>
                    <li>TODO: Only have some access based on role</li>
                </ul>

                <!-- Subscription Status -->
                <div class="rounded-lg border p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium">Subscription Status</h3>
                            <div class="mt-1 space-y-2">
                                <!-- Status Badge -->
                                <span
                                    v-if="subscription"
                                    :class="{
                                        'bg-green-100 text-green-800': subscription.status === 'active',
                                        'bg-yellow-100 text-yellow-800': subscription.status === 'trialing',
                                        'bg-red-100 text-red-800': ['cancelled', 'canceling'].includes(subscription.status),
                                    }"
                                    class="inline-flex rounded-full px-3 py-1 text-sm font-semibold"
                                >
                                    {{
                                        subscription.status === 'active'
                                            ? 'Active'
                                            : subscription.status === 'trialing'
                                              ? 'Trial'
                                              : subscription.status === 'canceling'
                                                ? 'Cancels at period end'
                                                : 'Cancelled'
                                    }}
                                </span>

                                <!-- Period Info -->
                                <div v-if="subscription?.status === 'active'" class="text-sm text-gray-600">
                                    <p v-if="subscription.onGracePeriod">
                                        Your subscription will end on {{ new Date(subscription.ends_at).toLocaleDateString() }}
                                    </p>
                                    <p v-else-if="subscription.nextPayment">
                                        Next payment on {{ new Date(subscription.nextPayment).toLocaleDateString() }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div>
                            <div v-if="!subscription">
                                <Button asChild>
                                    <Link href="/subscribe">Subscribe</Link>
                                </Button>
                            </div>
                            <!-- <Subscribe v-if="!subscription" /> -->
                            <CancelSubscription v-else-if="subscription.status === 'active' && !subscription.onGracePeriod" />
                        </div>
                    </div>

                    <!-- Subscription Details -->
                    <div v-if="subscription" class="mt-4 space-y-2">
                        <div v-if="subscription.trial_ends_at" class="flex justify-between text-sm">
                            <span class="text-gray-500">Trial Ends</span>
                            <span>{{ new Date(subscription.trial_ends_at).toLocaleDateString() }}</span>
                        </div>
                        <div v-if="subscription.ends_at" class="flex justify-between text-sm">
                            <span class="text-gray-500">Subscription Ends</span>
                            <span>{{ new Date(subscription.ends_at).toLocaleDateString() }}</span>
                        </div>
                        <div v-if="subscription.paused_at" class="flex justify-between text-sm">
                            <span class="text-gray-500">Paused Since</span>
                            <span>{{ new Date(subscription.paused_at).toLocaleDateString() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Transactions History -->
                <div class="rounded-lg border p-6">
                    <h3 class="text-lg font-medium">Billing History</h3>
                    <div class="mt-4">
                        <div v-if="transactions.length === 0" class="text-sm text-gray-500">No billing history available</div>
                        <div v-else class="space-y-4">
                            <div v-for="transaction in transactions" :key="transaction.id" class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div>
                                        <div class="text-sm font-medium">
                                            {{ new Date(transaction.billed_at).toLocaleDateString() }}
                                            <span v-if="transaction.invoice_number" class="text-gray-500">
                                                (Invoice #{{ transaction.invoice_number }})
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ formatCurrency(transaction.total, transaction.currency) }}
                                            <span v-if="transaction.tax" class="ml-2">
                                                (Tax: {{ formatCurrency(transaction.tax, transaction.currency) }})
                                            </span>
                                        </div>
                                        <div class="text-xs text-gray-400">Status: {{ transaction.status }}</div>
                                        <div class="text-xs text-gray-400">
                                            Invoice:
                                            <a :href="route('download-invoice', { id: transaction.id })" target="_blank">Download</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
