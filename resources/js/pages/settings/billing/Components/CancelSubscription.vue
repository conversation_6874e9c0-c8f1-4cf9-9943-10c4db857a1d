<script setup>
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const form = useForm({});
const isOpen = ref(false);

const cancelSubscription = () => {
    form.post(route('subscription.cancel'), {
        preserveScroll: true,
        onSuccess: () => {
            isOpen.value = false;
        },
    });
};
</script>

<template>
    <Dialog v-model:open="isOpen">
        <DialogTrigger asChild>
            <Button variant="destructive" size="sm">Cancel Subscription</Button>
        </DialogTrigger>
        <DialogContent>
            <DialogHeader>
                <DialogTitle>Cancel Subscription</DialogTitle>
                <DialogDescription>
                    Are you sure you want to cancel your subscription? Your subscription will remain active until the end of your current billing
                    period.
                </DialogDescription>
            </DialogHeader>
            <DialogFooter>
                <Button variant="outline" @click="isOpen = false"> Keep Subscription </Button>
                <Button variant="destructive" :disabled="form.processing" @click="cancelSubscription">
                    {{ form.processing ? 'Cancelling...' : 'Yes, Cancel Subscription' }}
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>
