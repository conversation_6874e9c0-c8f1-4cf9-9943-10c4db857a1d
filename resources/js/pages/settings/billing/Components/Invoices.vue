<script setup>
import { onMounted, ref } from 'vue';

const invoices = ref([]);

onMounted(() => {
    router.get(
        route('subscriptions.invoices'),
        {},
        {
            onSuccess: (page) => {
                invoices.value = page.props.invoices;
            },
        },
    );
});
</script>

<template>
    <div>
        <h2>Invoices</h2>
        <ul>
            <li v-for="invoice in invoices" :key="invoice.id">{{ invoice.date }} - {{ invoice.amount }}</li>
        </ul>
    </div>
</template>
