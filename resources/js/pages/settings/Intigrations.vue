<script setup lang="ts">
import { Head } from '@inertiajs/vue3';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Settings',
        href: '/settings',
    },
    {
        title: 'Intigrations',
        href: '/settings/team',
    },
];
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Intigrations" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall title="Intigrations" description="View and install your intigrations" />

                <!-- TODO: link this up -->
                <p>You can request an intigration here. You can also see the progress of intigrations on our roadmap page.</p>
                <div class="grid grid-cols-3 gap-4">
                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Freeagent</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://www.freeagent.com/">FreeAgent Central Ltd</a></span>
                            <Badge variant="secondary">Accountancy</Badge>
                        </div>
                        <p>Small business accounting & invoicing.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Zapier</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://zapier.com/">Zapier Inc.</a></span>
                            <Badge variant="secondary">Automation</Badge>
                        </div>
                        <p>Connects apps for workflow automation.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Google Calendar</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://calendar.google.com/">Google LLC</a></span>
                            <Badge variant="secondary">Scheduling</Badge>
                        </div>
                        <p>Syncs meetings, deadlines, and tasks.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Stripe</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://stripe.com/">Stripe Inc.</a></span>
                            <Badge variant="secondary">Payments</Badge>
                        </div>
                        <p>Online payment processing & invoicing.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">QuickBooks</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://quickbooks.intuit.com/">Intuit Inc.</a></span>
                            <Badge variant="secondary">Accounting</Badge>
                        </div>
                        <p>Bookkeeping, invoicing, and payroll.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Dropbox</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://www.dropbox.com/">Dropbox Inc.</a></span>
                            <Badge variant="secondary">Storage</Badge>
                        </div>
                        <p>File sharing & document storage.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Mailchimp</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://mailchimp.com/">Intuit Inc.</a></span>
                            <Badge variant="secondary">Marketing</Badge>
                        </div>
                        <p>Email campaigns & automation.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>

                    <Card class="col-span-1 flex flex-col gap-4 p-4">
                        <h2 class="text-2xl">Google Drive</h2>
                        <div class="flex flex-row items-center justify-between gap-2">
                            <span class="text-sm text-muted-foreground"><a href="https://www.google.com/drive/">Google LLC</a></span>
                            <Badge variant="secondary">Storage</Badge>
                        </div>
                        <p>Cloud file storage & sharing.</p>
                        <Button varient="secondary" disabled>Coming soon</Button>
                    </Card>
                </div>
            </div>

            <p>You can intigrate SCPIX/AMS too!</p>
            <div class="grid grid-cols-3 gap-4">
                <Card class="col-span-1 flex flex-col gap-4 p-4">
                    <h2 class="text-2xl">GMail</h2>
                    <div class="flex flex-row items-center justify-between gap-2">
                        <span class="text-sm text-muted-foreground"><a href="https://gmail.google.com/">Google LLC</a></span>
                        <Badge variant="secondary">Automation</Badge>
                    </div>
                    <p>Connects apps for workflow automation.</p>
                    <Button varient="secondary" disabled>Coming soon</Button>
                </Card>

                <Card class="col-span-1 flex flex-col gap-4 p-4">
                    <h2 class="text-2xl">Wordpress</h2>
                    <div class="flex flex-row items-center justify-between gap-2">
                        <span class="text-sm text-muted-foreground"><a href="https://wordpress.com/">Wordpress</a></span>
                        <Badge variant="secondary">Automation</Badge>
                    </div>
                    <p>Connects apps for workflow automation.</p>
                    <Button varient="secondary" disabled>Coming soon</Button>
                </Card>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
