<script setup>
import { But<PERSON> } from '@/components/ui/button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { useForm, usePage } from '@inertiajs/vue3';
import { UserMinusIcon } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const page = usePage();

const props = defineProps({
    member: Object,
});

const form = useForm({});

const removeMember = (member) => {
    form.delete(
        route('team.members.remove', {
            team: page.props.auth.user.current_team.id,
            member: member.id,
        }),
        {
            preserveScroll: true,
            onSuccess: () => {
                toast(`Member removed`, {
                    description: `${props.member.name} removed successfully`,
                    action: {
                        label: 'Undo',
                        onClick: () => console.log('Undo'),
                    },
                });
            },
        },
    );
};
</script>

<template>
    <Dialog>
        <DialogTrigger asChild>
            <Button variant="destructive"><UserMinusIcon /><span class="sr-only">Remove</span></Button>
        </DialogTrigger>
        <DialogContent>
            <DialogHeader>
                <DialogTitle>Remove member</DialogTitle>
                <DialogDescription> Are you sure you want to remove {{ member.name }} from the team? </DialogDescription>
            </DialogHeader>

            <DialogFooter>
                <DialogClose as-child>
                    <Button type="button" variant="secondary"> Cancel </Button>
                </DialogClose>
                <Button variant="destructive" @click="removeMember(member)" :disabled="form.processing"> Remove </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>
