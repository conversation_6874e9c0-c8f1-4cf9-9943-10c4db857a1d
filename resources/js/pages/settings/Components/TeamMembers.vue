<script setup lang="ts">
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { router } from '@inertiajs/vue3';
import { UserPlusIcon } from 'lucide-vue-next';
import RemoveMember from './RemoveMember.vue';
import UpdateMemberRole from './UpdateMemberRole.vue';

const props = defineProps({
    members: {
        type: Object,
        required: true,
    },
});

const goToPage = (url) => {
    if (url) {
        router.get(url, {}, { preserveState: true, replace: true });
    }
};
</script>

<template>
    <Card>
        <CardHeader>
            <CardTitle class="flex items-center justify-between">
                Team Members <Button variant="secondary"><UserPlusIcon /> Invite</Button></CardTitle
            >
            <CardDescription> Invite your team members to collaborate.</CardDescription>
        </CardHeader>
        <CardContent class="grid gap-6">
            <div class="flex items-center justify-between space-x-4" v-for="member in members.data" :key="member.id">
                <div class="flex items-center space-x-4">
                    <Avatar>
                        <AvatarImage src="https://randomuser.me/api/portraits/women/68.jpg" />
                        <AvatarFallback>OM</AvatarFallback>
                    </Avatar>
                    <div>
                        <p class="text-sm font-medium leading-none">{{ member.name }}</p>
                        <p class="text-sm text-muted-foreground">{{ member.email }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <UpdateMemberRole :member="member" />
                    <RemoveMember :member="member" />
                </div>
            </div>
        </CardContent>

        <CardFooter class="justify-end" v-if="!members.prev_page_url && !members.next_page_url">
            <div class="flex items-center justify-between">
                <Button variant="outline" :disabled="!members.prev_page_url" @click="goToPage(members.prev_page_url)" class="mr-2"> Previous </Button>

                <span>Page {{ members.current_page }}</span>

                <Button variant="outline" :disabled="!members.next_page_url" @click="goToPage(members.next_page_url)" class="ml-2"> Next </Button>
            </div>
        </CardFooter>
    </Card>
</template>
