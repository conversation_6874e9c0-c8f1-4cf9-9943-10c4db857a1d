<script setup>
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TransitionRoot } from '@headlessui/vue';
import { useForm } from '@inertiajs/vue3';

const props = defineProps({
    team: {
        type: Object,
        required: true,
    },
});

const form = useForm({
    name: props.team.name,
});

const submit = () => {
    form.patch(route('team.update', props.team), {
        preserveScroll: true,
    });
};
</script>

<template>
    <form @submit.prevent="submit">
        <!-- Team Name -->
        <div class="col-span-6 sm:col-span-4">
            <Label for="name" value="Team Name" />

            <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" />

            <InputError :message="form.errors.name" class="mt-2" />
        </div>

        <div class="col-span-6 sm:col-span-4">
            <Label for="email" value="Team Email" />

            <Input id="email" :placeholder="team.email" type="email" disabled class="mt-1 block w-full" />
        </div>

        <Button :disabled="form.processing">Save</Button>

        <TransitionRoot
            :show="form.recentlySuccessful"
            enter="transition ease-in-out"
            enter-from="opacity-0"
            leave="transition ease-in-out"
            leave-to="opacity-0"
        >
            <p class="text-sm text-neutral-600">Saved.</p>
        </TransitionRoot>
    </form>
</template>
