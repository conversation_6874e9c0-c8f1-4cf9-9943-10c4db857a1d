<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/components/ui/combobox';
import { cn } from '@/lib/utils';
import { router, usePage } from '@inertiajs/vue3';
import { CheckIcon, ChevronsUpDownIcon, SearchIcon } from 'lucide-vue-next';
import { computed } from 'vue';

const page = usePage();
const teams = computed(() => page.props.auth.user.all_teams);
const currentTeam = computed(() => page.props.auth.user.current_team);

const switchToTeam = (team_id) => {
    router.put(
        route('teams.switchTeam'),
        {
            team_id: team_id,
        },
        {
            preserveState: false,
        },
    );
};
</script>

<template>
    <div>
        <h1>View All Teams</h1>
    </div>

    <template v-if="teams.length >= 1">
        <Combobox :v-model="teams" by="name">
            <ComboboxAnchor as-child>
                <ComboboxTrigger as-child>
                    <Button variant="outline" class="justify-between">
                        {{ currentTeam?.name ?? 'Select team' }}

                        <ChevronsUpDownIcon class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                </ComboboxTrigger>
            </ComboboxAnchor>

            <ComboboxList>
                <div class="relative w-full max-w-sm items-center">
                    <ComboboxInput class="h-10 rounded-none border-0 border-b pl-9 focus-visible:ring-0" placeholder="Select framework..." />
                    <span class="absolute inset-y-0 start-0 flex items-center justify-center px-3">
                        <SearchIcon class="size-4 text-muted-foreground" />
                    </span>
                </div>

                <ComboboxEmpty> No team found. </ComboboxEmpty>

                <ComboboxGroup>
                    <ComboboxItem v-for="team in teams" :key="team.id" :value="team.id" class="gap-2 p-2" @click="switchToTeam(team.id)">
                        <div class="flex flex-row gap-2">
                            <div class="flex size-6 items-center justify-center rounded-sm border">
                                <!-- <component :is="team.logo" class="size-4 shrink-0" /> -->
                            </div>
                            {{ team.name }}
                        </div>

                        <ComboboxItemIndicator v-if="team.id === currentTeam.id">
                            <CheckIcon :class="cn('ml-auto h-4 w-4')" />
                        </ComboboxItemIndicator>
                    </ComboboxItem>
                </ComboboxGroup>
            </ComboboxList>
        </Combobox>
    </template>
</template>
