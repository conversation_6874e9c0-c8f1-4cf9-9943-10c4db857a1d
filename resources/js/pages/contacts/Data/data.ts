import { ArrowDownIcon, ArrowRightIcon, ArrowUpIcon, CheckCircleIcon, CircleIcon, InfoIcon, WatchIcon, XCircleIcon } from 'lucide-vue-next';
import { h } from 'vue';

export const labels = [
    {
        value: 'bug',
        label: 'Bug',
    },
    {
        value: 'feature',
        label: 'Feature',
    },
    {
        value: 'documentation',
        label: 'Documentation',
    },
];

export const statuses = [
    {
        value: 'backlog',
        label: 'Backlog',
        icon: h(InfoIcon),
    },
    {
        value: 'todo',
        label: 'Todo',
        icon: h(CircleIcon),
    },
    {
        value: 'in progress',
        label: 'In Progress',
        icon: h(WatchIcon),
    },
    {
        value: 'done',
        label: 'Done',
        icon: h(CheckCircleIcon),
    },
    {
        value: 'canceled',
        label: 'Canceled',
        icon: h(XCircleIcon),
    },
];

export const priorities = [
    {
        value: 'low',
        label: 'Low',
        icon: h(ArrowDownIcon),
    },
    {
        value: 'medium',
        label: 'Medium',
        icon: h(ArrowRightIcon),
    },
    {
        value: 'high',
        label: 'High',
        icon: h(ArrowUpIcon),
    },
];
