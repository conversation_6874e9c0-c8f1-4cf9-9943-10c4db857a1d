import { Checkbox } from '@/components/ui/checkbox';
import type { ColumnDef } from '@tanstack/vue-table';
import { h } from 'vue';
import type { Contact } from '../data/schema';
import DataTableColumnHeader from './DataTableColumnHeader.vue';
import DataTableRowActions from './DataTableRowActions.vue';

export const columns: ColumnDef<Contact>[] = [
    {
        id: 'select',
        header: ({ table }) =>
            h(Checkbox, {
                modelValue: table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate'),
                'onUpdate:modelValue': (value) => table.toggleAllPageRowsSelected(!!value),
                ariaLabel: 'Select all',
                class: 'translate-y-0.5',
            }),
        cell: ({ row }) =>
            h(Checkbox, {
                modelValue: row.getIsSelected(),
                'onUpdate:modelValue': (value) => row.toggleSelected(!!value),
                ariaLabel: 'Select row',
                class: 'translate-y-0.5',
            }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: ({ column }) => h(DataTableColumnHeader, { column, title: 'Name' }),
        cell: ({ row }) => h('div', { class: 'font-medium' }, row.getValue('name')),
    },
    {
        accessorKey: 'email',
        header: ({ column }) => h(DataTableColumnHeader, { column, title: 'Email' }),
        cell: ({ row }) => h('div', {}, row.getValue('email')),
    },
    {
        accessorKey: 'phone',
        header: ({ column }) => h(DataTableColumnHeader, { column, title: 'Phone' }),
        cell: ({ row }) => h('div', {}, row.getValue('phone') || '-'),
    },
    {
        accessorKey: 'job_position',
        header: ({ column }) => h(DataTableColumnHeader, { column, title: 'Position' }),
        cell: ({ row }) => h('div', {}, row.getValue('job_position') || '-'),
    },
    {
        accessorKey: 'contact_type',
        header: ({ column }) => h(DataTableColumnHeader, { column, title: 'Type' }),
        cell: ({ row }) => h('div', { class: 'capitalize' }, row.getValue('contact_type') || '-'),
        filterFn: (row, id, value) => {
            return value.includes(row.getValue(id));
        },
    },
    {
        id: 'actions',
        cell: ({ row }) => h(DataTableRowActions, { row }),
    },
];
