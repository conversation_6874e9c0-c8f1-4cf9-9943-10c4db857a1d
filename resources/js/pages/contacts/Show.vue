<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { LinkedinIcon, MailIcon, PhoneIcon } from 'lucide-vue-next';

const props = defineProps({
    contact: Array,
    client: Array,
});

// const breadcrumbs: BreadcrumbItem[] = [
//     {
//         title: 'Dashboard',
//         href: '/dashboard',
//     },
//     {
//         title: 'Contacts',
//         href: '/contacts',
//     },
//     {
//         title: props.contact.name,
//         href: '',
//     },
// ];

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/' },
    props.client ? { title: 'Clients', href: '/clients' } : { title: 'Contacts', href: '/contacts' },
    props.client ? { title: props.client.name, href: `/clients/${props.client.id}` } : null,
    { title: props.contact.name },
].filter((item) => item !== null);
</script>

<template>
    <Head title="Contacts" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <div class="grid grid-cols-3 gap-4">
                <div class="col-span-2">
                    <Badge v-if="contact.contact_type" variant="secondary" class="capitalize">
                        {{ contact.contact_type }}
                    </Badge>
                    <h1 class="text-2xl">{{ contact.name }}</h1>
                    <span v-if="contact.job_position" class="text-gray-600">
                        {{ contact.job_position }}
                    </span>
                    <span v-else class="text-gray-600 underline"> Add job postion </span>
                </div>
                <div class="col-span-1">
                    <Button v-if="contact.linkedin" as-child class="bg-[#0a66c2]">
                        <a :href="`${contact.linkedin}`" class="truncate" target="_blank">
                            <LinkedinIcon class="h-4 w-4" />
                            LinkedIn Account
                        </a>
                    </Button>
                    <Button v-else class="bg-[#0a66c2]">
                        <LinkedinIcon class="h-4 w-4" />
                        <span>Add LinkedIn</span>
                    </Button>
                </div>

                <Card class="col-span-2 flex flex-col gap-4 p-4">
                    <h2 class="text-xl">Contact information</h2>

                    <div class="flex items-center gap-2">
                        <MailIcon class="h-4 w-4" />
                        <a :href="`mailto:${contact.email}`" class="truncate">
                            {{ contact.email }}
                        </a>
                    </div>
                    <div class="flex items-center gap-2">
                        <PhoneIcon class="h-4 w-4" />
                        <a v-if="contact.phone" :href="`tel:${contact.phone}`">
                            {{ contact.phone }}
                        </a>
                        <span v-else class="text-gray-600 underline"> Add job Phone number </span>
                    </div>
                </Card>

                <Card class="flex flex-col gap-2 p-4">
                    <h2 class="text-xl">Data</h2>
                    <p class="text-xs"><strong>Created:</strong> {{ contact.created_at }}</p>
                    <p class="text-xs"><strong>Created by:</strong> {{ contact.created_by_name }}</p>
                    <p class="text-xs"><strong>Last updated:</strong> {{ contact.updated_at }}</p>
                </Card>

                <Card class="col-span-1 flex flex-col gap-2 p-4">
                    <h2 class="text-xl">Company</h2>
                    <Link v-if="company" :href="route('company.show', company.id)" class="text-xs">{{ company.name }}</Link>
                </Card>

                <Card class="col-span-1 flex flex-col gap-2 p-4">
                    <h2 class="text-xl">Projects</h2>
                </Card>

                <Card class="col-span-1 flex flex-col gap-2 p-4">
                    <h2 class="text-xl">Tasks</h2>
                </Card>
            </div>
        </div>
    </AppLayout>
</template>
