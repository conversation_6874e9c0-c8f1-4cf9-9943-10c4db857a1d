<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import DataTable from './Components/DataTable.vue';
import { columns } from './Components/columns';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Contacts',
        href: '/contacts',
    },
];

const props = defineProps({
    contacts: {
        type: Array,
        required: true,
    },
    companies: {
        type: Array,
        required: true,
    },
});
</script>

<template>
    <Head title="Contacts" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <h1>Contacts</h1>
            <DataTable :data="contacts" :columns="columns" />
        </div>
    </AppLayout>
</template>
