<script setup>
import { Link } from '@inertiajs/vue3';
import { defineProps } from 'vue';

const props = defineProps({
    teams: Array,
});
</script>

<template>
    <div>
        <h1>Teams</h1>
        <ul>
            <li v-for="team in teams" :key="team.id">
                {{ team.name }}
            </li>
        </ul>
        <Link :href="route('teams.create')">Create Team</Link>
    </div>
</template>
