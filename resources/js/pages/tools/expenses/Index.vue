<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import AddExpense from '../expenses/components/AddExpense.vue';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Expenses',
        href: '/expenses',
    },
];

const props = defineProps({
    expenses: {
        type: Array,
        required: true,
    },
});

const formatCurrency = (amount: string | number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
    }).format(typeof amount === 'string' ? parseFloat(amount) : amount);
};
</script>

<template>
    <Head title="Expenses" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <h1>Expenses</h1>
            <AddExpense />
            <ul>
                <li v-for="expense in expenses" :key="expense.id">
                    {{ expense.name }} - {{ formatCurrency(expense.amount, expense.currency) }} - {{ expense.date }}
                </li>
            </ul>
            Total Expenses:
            {{
                formatCurrency(
                    expenses.reduce((acc, expense) => acc + parseFloat(expense.amount), 0),
                    expenses[0].currency,
                )
            }}
        </div>
    </AppLayout>
</template>
