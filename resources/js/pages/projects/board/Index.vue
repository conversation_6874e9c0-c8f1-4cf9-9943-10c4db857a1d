<script setup lang="ts">
import { ScrollArea } from '@/components/ui/scroll-area';
import { ToggleGroup } from '@/components/ui/toggle-group';
import ToggleGroupItem from '@/components/ui/toggle-group/ToggleGroupItem.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import { ChartGanttIcon, KanbanIcon, TableIcon } from 'lucide-vue-next';
import { ref } from 'vue';
import TaskTable from '../components/TaskTable.vue';
import Kanban from '../components/kanban.vue';

const props = defineProps({
    project: {
        type: Object,
        required: true,
    },
});

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Projects',
        href: '/projects',
    },
    {
        title: props.project.name,
        href: '/projects/' + props.project.id,
    },
    {
        title: 'Tasks',
        href: '',
    },
];

// Define the status columns
const columns = [
    { id: 'backlog', title: 'Backlog', tasks: [] },
    { id: 'todo', title: 'To Do', tasks: [] },
    { id: 'in_progress', title: 'In Progress', tasks: [] },
    { id: 'review', title: 'Review', tasks: [] },
    { id: 'done', title: 'Done', tasks: [] },
];

const view = ref(localStorage.getItem(`projectview-${props.project.id}`) || 'kanban');

const setView = (newView: string) => {
    view.value = newView;
    localStorage.setItem(`projectview-${props.project.id}`, newView);
};
</script>

<template>
    <Head title="Task Board" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <ScrollArea class="">
            <ToggleGroup type="single" :defaultValue="view" class="gap-0 space-x-0">
                <ToggleGroupItem
                    value="kanban"
                    aria-label="Toggle Kanban view"
                    @click="setView('kanban')"
                    class="rounded-r-none bg-gray-100 data-[state=on]:bg-primary data-[state=on]:text-white"
                >
                    <KanbanIcon /> <span class="sr-only">Kanban View</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                    value="gantt"
                    aria-label="Toggle Gantt view"
                    @click="setView('gantt')"
                    class="rounded-none border-x-0 bg-gray-100 data-[state=on]:bg-primary data-[state=on]:text-white"
                >
                    <ChartGanttIcon /> <span class="sr-only">Gantt View</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                    value="table"
                    aria-label="Toggle table view"
                    @click="setView('table')"
                    class="rounded-l-none bg-gray-100 data-[state=on]:bg-primary data-[state=on]:text-white"
                >
                    <TableIcon /> <span class="sr-only">Table View</span>
                </ToggleGroupItem>
            </ToggleGroup>

            <div v-if="view === 'kanban'">
                <Kanban :project="project" :columns="columns" />
            </div>
            <div v-else-if="view === 'gantt'">gant</div>

            <div v-else-if="view === 'table'">
                <TaskTable :project="project" />
            </div>
        </ScrollArea>
    </AppLayout>
</template>
