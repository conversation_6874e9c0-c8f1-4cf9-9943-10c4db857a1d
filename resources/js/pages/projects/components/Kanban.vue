<script setup lang="ts">
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip } from '@/components/ui/tooltip';
import TooltipContent from '@/components/ui/tooltip/TooltipContent.vue';
import TooltipProvider from '@/components/ui/tooltip/TooltipProvider.vue';
import TooltipTrigger from '@/components/ui/tooltip/TooltipTrigger.vue';
import Task from '@/pages/projects/components/Task.vue';
import { router } from '@inertiajs/vue3';
import { EllipsisIcon } from 'lucide-vue-next';
import { ref } from 'vue';
import AddTask from './AddTask.vue';

const props = defineProps({
    project: {
        type: Object,
        required: true,
    },
    columns: {
        type: Array,
        required: true,
    },
});

// Group tasks by status
const groupedTasks = ref(
    props.columns.map((column) => ({
        ...column,
        tasks: props.project.tasks.filter((task) => task.status === column.id) || [],
    })),
);

const draggedTask = ref(null);
const draggedColumn = ref(null);
const draggedColumnIndex = ref(null);

const handleDragStart = (task, columnId) => {
    draggedTask.value = task;
    draggedColumn.value = columnId;
};

const handleDragOver = (e) => {
    e.preventDefault();
};

const handleDrop = (targetColumnId) => {
    if (!draggedTask.value || draggedColumn.value === targetColumnId) return;

    // Find source and target columns
    const sourceColumn = groupedTasks.value.find((col) => col.id === draggedColumn.value);
    const targetColumn = groupedTasks.value.find((col) => col.id === targetColumnId);

    if (!sourceColumn || !targetColumn) return;

    // Remove task from source column
    const taskIndex = sourceColumn.tasks.findIndex((task) => task.id === draggedTask.value.id);
    const [task] = sourceColumn.tasks.splice(taskIndex, 1);

    // Add task to target column
    targetColumn.tasks.push({ ...task, status: targetColumnId });

    // Update task status using Inertia
    router.put(
        route('tasks.update', draggedTask.value.id),
        {
            status: targetColumnId,
        },
        {
            preserveScroll: true,
            preserveState: true,
        },
    );

    draggedTask.value = null;
    draggedColumn.value = null;
};

const handleColumnDragStart = (columnIndex) => {
    draggedColumnIndex.value = columnIndex;
};

const handleColumnDragOver = (e) => {
    e.preventDefault();
};

const handleColumnDrop = (targetIndex) => {
    if (draggedColumnIndex.value === null || draggedColumnIndex.value === targetIndex) return;

    // Reorder the columns
    const columns = [...groupedTasks.value];
    const [movedColumn] = columns.splice(draggedColumnIndex.value, 1);
    columns.splice(targetIndex, 0, movedColumn);
    groupedTasks.value = columns;

    // Persist the new order to the backend
    router.put(
        route('projects.columns.reorder', props.project.id),
        {
            columns: columns.map((col) => col.id),
        },
        {
            preserveScroll: true,
            preserveState: true,
        },
    );

    draggedColumnIndex.value = null;
};

const isOpen = ref(false);
const selectedTask = ref(null);

const handleTaskClick = (task) => {
    selectedTask.value = task;
    isOpen.value = true;
};
</script>

<template>
    <ScrollArea class="h-[calc(100vh-10rem)]">
        <div class="flex gap-4 p-4">
            <div
                v-for="(column, index) in groupedTasks"
                :key="column.id"
                class="flex h-full w-72 flex-none flex-col rounded-lg bg-muted/50"
                draggable="true"
                @dragstart="handleColumnDragStart(index)"
                @dragover="handleColumnDragOver"
                @drop="handleColumnDrop(index)"
            >
                <!-- Column Header - make it look draggable -->
                <div class="flex cursor-move items-center justify-between p-3">
                    <h3 class="text-sm font-medium">{{ column.title }}</h3>
                    <div class="flex items-center gap-2">
                        <span class="rounded-full bg-muted px-2 py-1 text-xs font-medium">
                            {{ column.tasks.length }}
                        </span>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Button class="flex h-4 w-4 items-center text-muted-foreground hover:text-foreground"><EllipsisIcon /></Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Column settings</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                </div>

                <!-- Tasks Container -->
                <div class="flex flex-col gap-2 overflow-y-auto p-2">
                    <Card
                        v-for="task in column.tasks"
                        :key="task.id"
                        class="cursor-pointer bg-card hover:bg-accent/50"
                        draggable="true"
                        @click="(e) => handleTaskClick(task)"
                        @dragstart="handleDragStart(task, column.id)"
                    >
                        <div class="p-3">
                            <h4 class="text-sm font-medium">{{ task.name }}</h4>
                            <p class="mt-1 text-xs text-muted-foreground">{{ task.description }}</p>

                            <!-- Task Meta -->
                            <div class="mt-2 flex items-center gap-2">
                                <span v-if="task.due_date" class="flex items-center gap-1 rounded bg-muted px-1.5 py-0.5 text-xs">
                                    {{ new Date(task.due_date).toLocaleDateString() }}
                                </span>
                            </div>
                        </div>
                    </Card>
                </div>

                <!-- Add Task Button -->
                <div class="wrap mt-2 flex flex-row justify-between gap-2 p-3">
                    <AddTask :column-id="column.id" />
                </div>
            </div>
        </div>
    </ScrollArea>

    <Task v-model:open="isOpen" :task="selectedTask" />
</template>

<style scoped>
/* Custom scrollbar for columns */
.overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}
</style>
