<script setup>
const props = defineProps({
    project: {
        type: Object,
        required: true,
    },
});
</script>

<template>
    <table class="w-full table-auto border border-gray-200 text-left">
        <thead>
            <tr>
                <th>Name</th>
                <th>Description</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="task in project.tasks" :key="task.id">
                <td>{{ task.name }}</td>
                <td>{{ task.description }}</td>
                <td>{{ task.status }}</td>
            </tr>
        </tbody>
    </table>
</template>
