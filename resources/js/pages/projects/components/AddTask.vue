<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tooltip } from '@/components/ui/tooltip';
import TooltipContent from '@/components/ui/tooltip/TooltipContent.vue';
import TooltipProvider from '@/components/ui/tooltip/TooltipProvider.vue';
import TooltipTrigger from '@/components/ui/tooltip/TooltipTrigger.vue';
import { router } from '@inertiajs/vue3';
import { FilesIcon, PlusIcon, XIcon } from 'lucide-vue-next';
import { ref } from 'vue';

const props = defineProps<{
    columnId: string;
}>();

const isAdding = ref(false);
const taskName = ref('');

const handleSubmit = () => {
    if (!taskName.value.trim()) return;

    router.post(
        route('tasks.store'),
        {
            name: taskName.value,
            status: props.columnId,
            project_id: route().params.project,
        },
        {
            preserveScroll: true,
            onSuccess: () => {
                taskName.value = '';
                isAdding.value = false;
            },
        },
    );
};

const handleCancel = () => {
    isAdding.value = false;
    taskName.value = '';
};
</script>

<template>
    <div class="flex w-full flex-row justify-between" :class="{ 'flex-col': isAdding }">
        <div v-if="isAdding">
            <Card class="mb-2 p-2">
                <textarea
                    v-model="taskName"
                    class="min-h-[60px] w-full resize-none rounded border-0 bg-transparent p-1 text-sm placeholder:text-muted-foreground focus:outline-none"
                    placeholder="Enter a title for this task..."
                    @keydown.enter.prevent="handleSubmit"
                />
                <div class="mt-2 flex items-center gap-2">
                    <Button size="sm" @click="handleSubmit">Add task</Button>
                    <Button size="sm" variant="ghost" @click="handleCancel">
                        <XIcon class="h-4 w-4" />
                    </Button>
                </div>
            </Card>
        </div>
        <Button
            v-else
            variant="ghost"
            class="flex w-full items-center justify-start gap-1 text-sm text-muted-foreground hover:bg-accent/50 hover:text-foreground"
            @click="isAdding = true"
        >
            <PlusIcon class="h-4 w-4" /> Add a task
        </Button>

        <TooltipProvider v-if="!isAdding">
            <Tooltip>
                <TooltipTrigger>
                    <Button variant="icon" aria-label="Add from template" class="text-lg text-muted-foreground hover:text-foreground">
                        <FilesIcon class="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Add task from template</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    </div>
</template>
