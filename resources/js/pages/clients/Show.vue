<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    client: Object,
});

console.log(props.client);

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Clients',
        href: '/clients',
    },
    {
        title: props.client.name,
        href: '',
    },
];
</script>

<template>
    <Head :title="client.name" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <div class="grid grid-cols-3 gap-4">
                <div class="col-span-2">
                    <h1 class="text-2xl">{{ client.name }}</h1>
                </div>
            </div>

            <p>{{ client.email }}</p>
            <p>{{ client.phone }}</p>
            <p>{{ client.linkedin }}</p>
            <p>Website: {{ client.website }}</p>
            <p>Address: {{ client.address }}</p>
            <p>Created by: {{ client.created_by_name }}</p>

            <div>
                <p>Contacts</p>
                <ul v-if="client.contacts">
                    <li v-for="contact in client.contacts" :key="contact.id">
                        <a :href="`/contacts/${contact.id}`">{{ contact.name }}</a>
                    </li>
                </ul>
            </div>

            <div>
                <p>Projects</p>
            </div>

            <div>
                <p>Tasks</p>
            </div>

            <div>
                <p>Invoices</p>
            </div>

            <div>
                <p>Comments / Notes</p>
            </div>

            <div>
                <p>Files</p>
            </div>

            <div>
                <p>Activity</p>
            </div>
        </div>
    </AppLayout>
</template>
