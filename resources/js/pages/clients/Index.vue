<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Clients',
        href: '/clients',
    },
];

const props = defineProps({
    companies: {
        type: Array,
        required: true,
    },
});
</script>

<template>
    <Head title="Clients" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <h1>Clients</h1>
            <p>Task: List out the companies, show live projects</p>
            <ul v-if="companies">
                <li v-for="client in companies" :key="client.id">
                    <a :href="`/clients/${client.id}`">{{ client.name }}</a>
                </li>
            </ul>
        </div>
    </AppLayout>
</template>
