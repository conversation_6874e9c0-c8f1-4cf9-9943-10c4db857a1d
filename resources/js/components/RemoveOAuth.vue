<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';

// Components
import HeadingSmall from '@/components/HeadingSmall.vue';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { GithubIcon } from 'lucide-vue-next';

const form = useForm({});

const unlinkGithubAccount = () => {
    form.patch(route('profile.unlinkGithub'), {
        preserveScroll: true,
        onSuccess: closeModal,
    });
};

const closeModal = () => {
    form.clearErrors();
    form.reset();
};
</script>

<template>
    <div class="space-y-6">
        <HeadingSmall title="Link social accounts" description="This enables you to sign into your account using the accounts below" />

        <div class="space-y-4 p-4">
            <Dialog>
                <DialogTrigger as-child>
                    <Button class="w-full" @click="unlinkGithubAccount"> <GithubIcon /> Unlink GitHub account </Button>
                </DialogTrigger>
                <DialogContent>
                    <form class="space-y-6" @submit="unlinkGithubAccount">
                        <DialogHeader class="space-y-3">
                            <DialogTitle>Are you sure you want to unlink your social account?</DialogTitle>
                            <DialogDescription>
                                You may have to reset your password to re-gain access to your account after (if you have not already set yopur own
                                password)
                            </DialogDescription>
                        </DialogHeader>

                        <DialogFooter class="gap-2">
                            <DialogClose as-child>
                                <Button variant="secondary" @click="closeModal"> Cancel </Button>
                            </DialogClose>

                            <Button variant="destructive" :disabled="form.processing">
                                <button type="submit">Remove account</button>
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    </div>
</template>
