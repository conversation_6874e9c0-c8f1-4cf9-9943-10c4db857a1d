<!-- <script setup>
import { defineProps } from 'vue';
import { Button } from './ui/button';

const props = defineProps({
    checkout: {
        type: Object,
        required: true,
    },
});

console.log(props.checkout);

// Function to open Paddle checkout
const openPaddleCheckout = () => {
    if (window.Paddle) {
        const options = {
            items: props.checkout.items,
            customerId: props.checkout.customerId,
            customData: props.checkout.customData,
            successUrl: props.checkout.successUrl,
        };

        // Open Paddle checkout
        window.Paddle.Checkout.open(options);
    } else {
        console.error('Paddle is not loaded.');
    }
};
</script>

<template>
    <Button asChild>
        <a
            href="#!"
            class="paddle_button"
            :data-items="JSON.stringify(checkout.items)"
            :data-customer-id="checkout.customerId"
            :data-custom-data="checkout.customData ? JSON.stringify(checkout.customData) : null"
            :data-success-url="checkout.successUrl"
            @click.prevent="openPaddleCheckout"
        >
            Subscribe
        </a>
    </Button>
</template> -->

<script setup>
import { useAppearance } from '@/composables/useAppearance';
import { onMounted, watch } from 'vue';

const props = defineProps({
    checkout: {
        type: Object,
        required: true,
    },
});

const { appearance } = useAppearance();

const initializePaddleCheckout = () => {
    if (!window.Paddle) return;

    // Add a small delay to ensure DOM is ready
    setTimeout(() => {
        window.Paddle.Checkout.open({
            ...props.checkout.options,
            settings: {
                // displayMode: 'inline',
                frameTarget: 'paddle-checkout',
                frameInitialHeight: 450,
                frameStyle: 'width: 100%; min-width: 312px; background-color: transparent; border: none;',
                theme: appearance.value === 'dark' ? 'dark' : 'light',
                variant: 'one-page',
            },
            customer: {
                email: props.checkout.options.customerEmail,
                name: props.checkout.options.customerName,
            },
        });
    }, 100);
};

// Initialize when mounted
onMounted(initializePaddleCheckout);

// Re-initialize when appearance changes
watch(appearance, initializePaddleCheckout, { immediate: true });
</script>

<template>
    <div class="paddle-checkout"></div>
</template>
