<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';

interface Props {
    class?: string;
}

defineProps<Props>();
</script>

<template>
    <div class="flex aspect-square size-24 items-center justify-center rounded-md">
        <AppLogoIcon class="fill-black text-black dark:fill-white dark:text-white" />
        <span class="sr-only">HiveFlo</span>
    </div>
</template>
