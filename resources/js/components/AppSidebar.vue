<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

import {
    AxeIcon,
    BookA,
    BookOpenIcon,
    CalendarDaysIcon,
    ChartBarIncreasingIcon,
    ChartNoAxesGanttIcon,
    CircleHelpIcon,
    ContactIcon,
    LayoutDashboardIcon,
    ReceiptIcon,
    SignatureIcon,
    SquareKanbanIcon,
} from 'lucide-vue-next';
import TeamSwitcher from './TeamSwitcher.vue';
const page = usePage<SharedData>();

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutDashboardIcon,
        prefetch: true,
        isActive: route().current('dashboard'),
    },
    {
        title: 'Calendar',
        href: '/calendar',
        icon: CalendarDaysIcon,
        isActive: route().current('calendar.*'),
    },
    {
        title: 'Clients',
        href: '/clients',
        icon: BookA,
        isActive: route().current('clients.*'),
    },
    {
        title: 'Projects',
        href: '/projects',
        icon: SquareKanbanIcon,
        isActive: route().current('projects.*'),
        items: [
            {
                title: 'Dashboard',
                href: '/projects',
                isActive: route().current('projects.*'),
            },
            {
                title: 'All Projects',
                href: '/projects/all',

                isActive: route().current('projects/all'),
            },
            {
                title: 'Tasks',
                href: '/projects/tasks',
                isActive: route().current('projects/tasks.*'),
            },
        ],
    },
    {
        title: 'Sales Pipeline',
        href: '/sales',
        icon: ChartNoAxesGanttIcon,
        isActive: route().current('sales.*'),
        items: [
            {
                title: 'Overview',
                href: '/sales',
                isActive: route().current('sales'),
            },
            {
                title: 'Deals',
                href: '/sales/deals',
                isActive: route().current('sales/deals'),
            },
        ],
    },
    {
        title: 'Contacts',
        href: '/contacts',
        icon: ContactIcon,
        isActive: route().current('contacts.*'),
    },
    {
        title: 'Invoices & Payments',
        href: '/invoices',
        icon: ReceiptIcon,
        isActive: route().current('invoices.*'),
        items: [
            {
                title: 'Overview',
                href: '/invoices',
            },
            {
                title: 'All invoices',
                href: '/invoices/all',
            },
            {
                title: 'Payments',
                href: '/invoices/payments',
            },
        ],
    },
    {
        title: 'Proposals & Contracts',
        href: '/proposals',
        icon: SignatureIcon,
        isActive: route().current('invoices.*'),
        items: [
            {
                title: 'Proposals',
                href: '/proposals',
            },
            {
                title: 'Contracts',
                href: '/contracts',
            },
        ],
    },
    {
        title: 'Documents',
        href: '/documents',
        icon: BookOpenIcon,
        isActive: route().current('documents.*'),
    },
    {
        title: 'Support',
        href: '/support',
        icon: CircleHelpIcon,
        items: [
            {
                title: 'Tickets',
                href: '/support/tickets',
            },
            {
                title: 'Knowledge Base',
                href: '/support/knowledge-base',
            },
        ],
    },
    {
        title: 'Reports',
        href: '/reports',
        icon: ChartBarIncreasingIcon,
        items: [
            {
                title: 'Overview',
                href: '/reports',
            },
            {
                title: 'Financial Reports',
                href: '/reports/financial',
            },
            {
                title: 'Project Reports',
                href: '/reports/projects',
            },
            {
                title: 'Sales Reports',
                href: '/reports/sales',
            },
        ],
    },
    {
        title: 'Tools',
        href: '/tools',
        icon: AxeIcon,
        items: [
            {
                title: 'Expense Manager',
                href: '/expenses',
            },
            {
                title: 'Template Manager',
                href: '/templates',
            },
        ],
    },
    // {
    //     title: 'Settings',
    //     href: '#',
    //     icon: Settings2Icon,
    //     items: [
    //         {
    //             title: 'General',
    //             href: '#',
    //         },
    //         {
    //             title: 'Intigrations',
    //             href: '#',
    //         },
    //         {
    //             title: 'Import',
    //             href: '/settings/import',
    //         },
    //         {
    //             title: 'Team',
    //             href: '#',
    //         },
    //         {
    //             title: 'Billing',
    //             href: '#',
    //         },
    //         {
    //             title: 'Activity Log',
    //             href: '/activity-log',
    //         },
    //         {
    //             title: 'Trash',
    //             href: '/trash',
    //         },
    //     ],
    // },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Documentation',
        href: '/docs',
        icon: BookOpenIcon,
    },
];

const teams = computed(() => page.props.auth.user.all_teams);
const currentTeam = computed(() => page.props.auth.user.current_team);
</script>

<template>
    <Sidebar collapsible="icon">
        <SidebarHeader>
            <SidebarMenu>
                <TeamSwitcher :teams="teams" :currentTeam="currentTeam" />
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
