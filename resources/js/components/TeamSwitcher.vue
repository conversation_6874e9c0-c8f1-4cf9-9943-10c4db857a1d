<script setup lang="ts">
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { Link, router } from '@inertiajs/vue3';
import { ChevronsUpDownIcon, InfinityIcon, PlusIcon, Settings2Icon } from 'lucide-vue-next';

const { isMobile } = useSidebar();
const props = defineProps<{
    teams: {
        id: string;
        name: string;
    }[];
    currentTeam: {
        id: string;
        name: string;
    };
}>();

const switchToTeam = (team_id) => {
    router.put(
        route('teams.switchTeam'),
        {
            team_id: team_id,
        },
        {
            preserveState: false,
        },
    );
};
</script>
<template>
    <SidebarMenu>
        <SidebarMenuItem>
            <DropdownMenu>
                <DropdownMenuTrigger as-child>
                    <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                        <div
                            class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground"
                        >
                            <!-- <component :is="activeTeam.logo" class="size-4" /> -->
                        </div>
                        <div class="grid flex-1 text-left text-sm leading-tight">
                            <span class="truncate font-semibold">
                                {{ currentTeam?.name ?? 'No team' }}
                            </span>
                            <!-- <span class="truncate text-xs">{{ activeTeam.plan }}</span> -->
                        </div>
                        <ChevronsUpDownIcon class="ml-auto" />
                    </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                    align="start"
                    :side="isMobile ? 'bottom' : 'right'"
                    :side-offset="4"
                >
                    <template v-if="teams.length >= 1">
                        <DropdownMenuLabel class="text-xs text-muted-foreground"> Teams </DropdownMenuLabel>

                        <DropdownMenuItem v-for="team in teams.slice(0, 5)" :key="team.id" class="gap-2 p-2" @click="switchToTeam(team.id)">
                            <div class="flex size-6 items-center justify-center rounded-sm border">
                                <!-- <component :is="team.logo" class="size-4 shrink-0" /> -->
                            </div>
                            {{ team.name }}
                        </DropdownMenuItem>
                        <Link :href="route('team.edit')" v-if="teams.length > 5">
                            <DropdownMenuItem class="cursor-pointer gap-2 p-2">
                                <div class="flex size-6 items-center justify-center rounded-md border bg-background">
                                    <InfinityIcon class="size-4" />
                                </div>
                                <div class="font-medium">View all teams</div>
                            </DropdownMenuItem>
                        </Link>
                        <DropdownMenuSeparator />
                    </template>
                    <Link v-if="currentTeam" :href="route('team.edit')">
                        <DropdownMenuItem class="cursor-pointer gap-2 p-2">
                            <div class="flex size-6 items-center justify-center rounded-md border bg-background">
                                <Settings2Icon class="size-4" />
                            </div>
                            <div class="font-medium">Team Settings</div>
                        </DropdownMenuItem>
                    </Link>
                    <Link class="cursor-pointer font-medium text-muted-foreground" :href="route('teams.create')">
                        <DropdownMenuItem class="gap-2 p-2">
                            <div class="flex size-6 items-center justify-center rounded-md border bg-background">
                                <PlusIcon class="size-4" />
                            </div>
                            Add team
                        </DropdownMenuItem>
                    </Link>
                </DropdownMenuContent>
            </DropdownMenu>
        </SidebarMenuItem>
    </SidebarMenu>
</template>
