<script setup lang="ts">
import { SidebarProvider } from '@/components/ui/sidebar';
import { onMounted, onUnmounted, ref } from 'vue';

const isOpen = ref(true);

const updateSidebarState = () => {
    isOpen.value = localStorage.getItem('sidebar') !== 'false';
};

onMounted(() => {
    updateSidebarState();
    window.addEventListener('storage', updateSidebarState);
});

onUnmounted(() => {
    window.removeEventListener('storage', updateSidebarState);
});

const handleSidebarChange = (open: boolean) => {
    isOpen.value = open;
    localStorage.setItem('sidebar', String(open));
};
</script>

<template>
    <SidebarProvider :default-open="isOpen" :open="isOpen" @update:open="handleSidebarChange">
        <slot />
    </SidebarProvider>
</template>
