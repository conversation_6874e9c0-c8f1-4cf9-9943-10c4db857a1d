<script setup>
import { ScrollA<PERSON> } from '@/components/ui/scroll-area';
import { CircleCheckIcon } from 'lucide-vue-next';
</script>

<template>
    <ScrollArea class="h-[400px] w-full rounded-md border p-4">
        <ul class="grid gap-2">
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> 1 Team</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> 5 members</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Invoicing</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Unlimited Projects</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Unlimited Contacts & Organisations</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Client billing</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Client portal</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Helpdesk</li>
            <li class="flex gap-2"><CircleCheckIcon class="text-green-700" /> Contracts and proposals</li>
        </ul>
    </ScrollArea>

    <small>
        *Subject to fair use policy. <a href="/terms">Learn more <span class="sr-only">about our fair use policy</span>.</a>
    </small>
</template>
