import type { PageProps } from '@inertiajs/core';
import type { LucideIcon } from 'lucide-vue-next';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
    prefetch?: boolean;
    items?: NavItem[];
}

export interface SharedData extends PageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    github_id: string | null;
    created_at: string;
    updated_at: string;
    all_teams: Team[];
    current_team: Team;
}

export interface Team {
    id: number;
    name: string;
}

export interface Subscription {
    id: string;
    billable_id: string;
    billable_type: string;
    type: string;
    paddle_id: string;
    status: 'active' | 'trialing' | 'cancelled';
    trial_ends_at: string | null;
    paused_at: string | null;
    ends_at: string | null;
    created_at: string;
    updated_at: string;
    items?: SubscriptionItem[];
}

export interface SubscriptionItem {
    id: string;
    subscription_id: string;
    product_id: string;
    price_id: string;
    status: string;
    quantity: number;
    created_at: string;
    updated_at: string;
}

export interface Transaction {
    id: string;
    billable_id: string;
    billable_type: string;
    paddle_id: string;
    paddle_subscription_id: string | null;
    invoice_number: string | null;
    status: string;
    total: string;
    tax: string;
    currency: string;
    billed_at: string;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;
